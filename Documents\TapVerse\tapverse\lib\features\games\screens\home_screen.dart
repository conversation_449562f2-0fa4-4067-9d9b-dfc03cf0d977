import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import '../widgets/game_grid.dart';
import '../widgets/token_display.dart';
import '../widgets/account_upgrade_banner.dart';
import '../../casino/widgets/floating_casino_button.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);
    
    await soundService.initialize();
    await vibrationService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);
    final userTokens = ref.watch(userTokensProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'TapVerse',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => gameNavigation.navigateToSettings(context),
          tooltip: 'Settings',
        ),
        actions: [
          // User switching button
          IconButton(
            icon: const Icon(Icons.switch_account),
            onPressed: () => _showUserSwitchDialog(context, ref),
            tooltip: 'Switch User',
          ),
          // Profile button
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () => gameNavigation.navigateToProfile(context),
            tooltip: 'Profile',
          ),
          // Store button
          IconButton(
            icon: const Icon(Icons.store),
            onPressed: () => gameNavigation.navigateToStore(context),
            tooltip: 'Store',
          ),
          // Token display
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: userTokens.when(
              data: (tokens) => TokenDisplay(tokens: tokens),
              loading: () => const TokenDisplay(tokens: 0),
              error: (_, __) => const TokenDisplay(tokens: 0),
            ),
          ),
        ],
      ),
      body: authState.when(
        data: (user) {
          if (user == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          return Stack(
            children: [
              Column(
                children: [
                  // Account upgrade banner for anonymous users
                  const AccountUpgradeBanner(),

                  // Welcome section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.primaryContainer,
                          Theme.of(context).colorScheme.secondaryContainer,
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back, ${user.displayName ?? 'Player'}!',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Choose a game to start earning tokens',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Quick stats
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Games',
                            '${gameNavigation.allGames.length}',
                            Icons.games,
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Leaderboard',
                            'View',
                            Icons.leaderboard,
                            Colors.green,
                            onTap: () => gameNavigation.navigateToLeaderboard(context),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            context,
                            'Store',
                            'Shop',
                            Icons.store,
                            Colors.purple,
                            onTap: () => gameNavigation.navigateToStore(context),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Expanded Games grid (takes more space now)
                  const Expanded(
                    child: GameGrid(),
                  ),
                ],
              ),

              // Floating casino button
              const FloatingCasinoButton(),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Something went wrong',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(authStateProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                value,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUserSwitchDialog(BuildContext context, WidgetRef ref) {
    final userSwitchingService = ref.read(userSwitchingServiceProvider);
    final currentUserInfo = userSwitchingService.getCurrentUserInfo();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.switch_account, color: Colors.blue),
            SizedBox(width: 8),
            Text('Account Management'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (currentUserInfo != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current User',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currentUserInfo.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          currentUserInfo.isAnonymous ? Icons.person_outline : Icons.person,
                          size: 16,
                          color: currentUserInfo.isAnonymous ? Colors.orange : Colors.green,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          currentUserInfo.isAnonymous ? 'Anonymous Account' : 'Registered Account',
                          style: TextStyle(
                            color: currentUserInfo.isAnonymous ? Colors.orange : Colors.green,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    if (currentUserInfo.email != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        currentUserInfo.email!,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Choose an action:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (userSwitchingService.canUpgradeAccount())
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Navigate to account upgrade
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Account upgrade feature coming soon!'),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Upgrade Account'),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSignOutOptions(context, ref);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Switch User'),
          ),
        ],
      ),
    );
  }

  void _showSignOutOptions(BuildContext context, WidgetRef ref) {
    final userSwitchingService = ref.read(userSwitchingServiceProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.logout, color: Colors.orange),
            SizedBox(width: 8),
            Text('Switch User'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('How would you like to switch users?'),
            SizedBox(height: 8),
            Text(
              '• Sign Out: Return to login screen\n• New Anonymous: Start fresh anonymously',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await userSwitchingService.switchToAnonymous();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success
                      ? 'Switched to new anonymous account'
                      : 'Failed to switch accounts'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('New Anonymous'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await userSwitchingService.signOutCurrentUser();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success
                      ? 'Signed out successfully'
                      : 'Failed to sign out'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
