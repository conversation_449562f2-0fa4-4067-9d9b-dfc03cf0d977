import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/game_navigation_service.dart';
import '../../../core/providers/app_providers.dart';

class GameCard extends ConsumerWidget {
  final GameInfo gameInfo;
  final VoidCallback onTap;
  final bool isPinned;
  final VoidCallback? onPinToggle;

  const GameCard({
    super.key,
    required this.gameInfo,
    required this.onTap,
    this.isPinned = false,
    this.onPinToggle,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authService = ref.watch(authServiceProvider);
    final user = authService.currentUser;

    return Card(
      elevation: isPinned ? 6 : 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isPinned
          ? BorderSide(color: Colors.amber.shade400, width: 2)
          : BorderSide.none,
      ),
      child: Ink<PERSON>ell(
        onTap: onTap,
        onLongPress: onPinToggle,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                gameInfo.primaryColor.withValues(alpha: isPinned ? 0.15 : 0.1),
                gameInfo.secondaryColor.withValues(alpha: isPinned ? 0.1 : 0.05),
              ],
            ),
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Game icon and difficulty
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: gameInfo.primaryColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            gameInfo.icon,
                            size: 32,
                            color: gameInfo.primaryColor,
                          ),
                        ),
                        _buildDifficultyIndicator(),
                      ],
                    ),

                const SizedBox(height: 12),

                // Game name
                Text(
                  gameInfo.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: gameInfo.primaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // Game description
                Text(
                  gameInfo.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const Spacer(),

                // Tags
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: gameInfo.tags.take(2).map((tag) => _buildTag(tag)).toList(),
                ),

                const SizedBox(height: 8),

                // High score (if available)
                if (user != null)
                  FutureBuilder<int>(
                    future: _getUserHighScore(ref, user.uid),
                    builder: (context, snapshot) {
                      final highScore = snapshot.data ?? 0;
                      return Row(
                        children: [
                          Icon(
                            Icons.star,
                            size: 16,
                            color: gameInfo.secondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            highScore > 0 ? 'Best: $highScore' : 'Not played',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: gameInfo.secondaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
              ],
            ),
          ),

          // Pin indicator
          if (isPinned)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.amber.shade400,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.amber.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.push_pin,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    ),
  );
  }

  Widget _buildDifficultyIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < gameInfo.difficulty ? Icons.star : Icons.star_border,
          size: 12,
          color: gameInfo.primaryColor,
        );
      }),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: gameInfo.secondaryColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        tag,
        style: TextStyle(
          fontSize: 10,
          color: gameInfo.secondaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Future<int> _getUserHighScore(WidgetRef ref, String uid) async {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);
      final user = await firestoreService.getUser(uid);
      return user?.getHighScore(gameInfo.id) ?? 0;
    } catch (e) {
      return 0;
    }
  }
}
