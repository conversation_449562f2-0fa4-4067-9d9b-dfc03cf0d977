Phase 3: Add All 23 Mini-Games
Objectives:

One game = one feature folder

Each game gets: game widget, assets, sounds, score logic

Tasks:

1 game at a time: each folder like lib/features/games/flappy/flappy_game.dart

🧠 Prompt:

“Create a Flutter widget FlappyGame in lib/features/games/flappy/flappy_game.dart. Tapping makes the bird flap upward. Pass through pipes. Score = number of pipes passed. Show GameOverDialog. Submit score to Firestore and reward tokens.”

Repeat this for each of the 23 games:
1. Basketball Flick
diff
Copy
Edit
Create a widget called `BasketballFlickGame` in `lib/features/games/basketball_flick/basketball_flick_game.dart`.

Game: Basketball Flick
Interaction Type: Flick

Gameplay:
- Flick a basketball toward a moving hoop.
- Score if ball enters hoop. Reset ball after shot.
- End game after 3 missed shots.

Visuals:
- Animate ball motion with flutter_animate.
- Use Lottie animation and sound (`swish.mp3`) on score.
- Trigger vibration on successful shot.

Game Over:
- Show GameOverDialog (score, high score, tokens = score × 2).
- Submit score to `leaderboards/basketball_flick`
- Update `users/{uid}/tokens`
2. Soccer Keepy-Uppy
markdown
Copy
Edit
Create a widget called `SoccerKeepyUppyGame` in `lib/features/games/soccer_keepy_uppy/soccer_keepy_uppy_game.dart`.

Game: Soccer Keepy-Uppy
Interaction Type: Tap

Gameplay:
- Tap the ball to bounce it upward.
- Each tap adds 1 to score.
- Ball falls to bottom = game over.

Visuals:
- Animate bounce and ball motion.
- Play `kick.mp3` and vibrate on tap.

Game Over:
- Show GameOverDialog with tokens = score × 1
- Update Firestore and token count
3. Single-Player Pong
vbnet
Copy
Edit
Widget: `PongGame` in `lib/features/games/pong/pong_game.dart`

Interaction Type: Drag

Gameplay:
- Ball bounces around screen.
- Drag paddle to reflect ball.
- Ball gets faster with each hit. Miss = game over.

Visuals:
- Animate paddle/ball with flutter_animate.
- Play `bounce.mp3`, vibrate on hit.

Game Over:
- GameOverDialog, tokens = score × 2
- Leaderboard: `leaderboards/pong`
4. Volleyball Keep-Alive
vbnet
Copy
Edit
Widget: `VolleyballGame` in `lib/features/games/volleyball/volleyball_game.dart`

Tap to keep ball bouncing, ends when it touches the ground. Score increases per bounce.
5. Flappy Bird
swift
Copy
Edit
Widget: `FlappyGame` in `lib/features/games/flappy/flappy_game.dart`

Tap to flap and pass through pipes. Score = pipes passed.
6. Brick Breaker
swift
Copy
Edit
Widget: `BrickBreakerGame` in `lib/features/games/brick_breaker/brick_breaker_game.dart`

Drag paddle to bounce ball and break bricks.
7. Asteroid Blaster
vbnet
Copy
Edit
Widget: `AsteroidBlasterGame` in `lib/features/games/asteroid_blaster/asteroid_blaster_game.dart`

Drag to move spaceship, tap to shoot. Score = asteroids destroyed.
8. Snake
swift
Copy
Edit
Widget: `SnakeGame` in `lib/features/games/snake/snake_game.dart`

Swipe to change direction. Eat to grow. Don’t hit self or wall.
9. Color Match
swift
Copy
Edit
Widget: `ColorMatchGame` in `lib/features/games/color_match/color_match_game.dart`

Tap to rotate color ring. Match falling color balls. Miss = game over.
10. Runner
swift
Copy
Edit
Widget: `RunnerGame` in `lib/features/games/runner/runner_game.dart`

Swipe left/right/up/down to dodge obstacles. Score by distance.
11. Piano Tiles
swift
Copy
Edit
Widget: `PianoTilesGame` in `lib/features/games/piano_tiles/piano_tiles_game.dart`

Tap only black tiles in rhythm. Speed increases. Miss = game over.
12. Minesweeper
swift
Copy
Edit
Widget: `MinesweeperGame` in `lib/features/games/minesweeper/minesweeper_game.dart`

Classic logic game. Tap to reveal, flag mines. Game ends if you hit a mine.
13. Catch Game
swift
Copy
Edit
Widget: `CatchGame` in `lib/features/games/catch_game/catch_game.dart`

Drag basket to catch falling fruits or coins.
14. Number Merge (2048)
swift
Copy
Edit
Widget: `NumberMergeGame` in `lib/features/games/number_merge/number_merge_game.dart`

Swipe to merge tiles of same value. Score = highest tile.
15. Darts Flick
swift
Copy
Edit
Widget: `DartsGame` in `lib/features/games/darts_flick/darts_flick_game.dart`

Flick to aim at a dartboard. Score = center hits.
16. Dropper
vbnet
Copy
Edit
Widget: `DropperGame` in `lib/features/games/dropper/dropper_game.dart`

Tap to drop item from above. Try to land in a moving bucket.
17. Space Invaders
vbnet
Copy
Edit
Widget: `SpaceInvadersGame` in `lib/features/games/space_invaders/space_invaders_game.dart`

Drag to move ship, tap to shoot. Clear waves of aliens.
18. Match-3 Puzzle
go
Copy
Edit
Widget: `Match3Game` in `lib/features/games/match_3/match_3_game.dart`

Drag tiles to make 3+ matches. Score = combos.
19. Platform Jumper
swift
Copy
Edit
Widget: `PlatformJumperGame` in `lib/features/games/platform_jumper/platform_jumper_game.dart`

Tap to jump between platforms. Score = platforms cleared.
20. Mini Golf
vbnet
Copy
Edit
Widget: `MiniGolfGame` in `lib/features/games/mini_golf/mini_golf_game.dart`

Flick to shoot ball into hole. Fewer strokes = better score.
21. Balloon Pop
swift
Copy
Edit
Widget: `BalloonPopGame` in `lib/features/games/balloon_pop/balloon_pop_game.dart`

Tap balloons before they float off screen. Score = number popped.
22. Archery
swift
Copy
Edit
Widget: `ArcheryGame` in `lib/features/games/archery/archery_game.dart`

Drag and release to shoot arrows at target. Score = accuracy.
23. Block Stacker
vbnet
Copy
Edit
Widget: `BlockStackerGame` in `lib/features/games/block_stacker/block_stacker_game.dart`

Tap to drop block. Try to stack aligned for higher tower.