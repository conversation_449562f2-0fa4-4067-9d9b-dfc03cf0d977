Phase 5: In-Game Store & Customization
Objectives:

“Create a Flutter shop screen that lists items from Firestore /store/items. If the user has enough tokens, allow purchase and store the item in /users/{uid}/inventory/{item_id}.”

Users spend tokens to buy items (e.g., skins, effects)

Inventory system per user

Tasks:

Create store screen:

Load from /store/items

Show item name, price, preview

Purchase logic:

Deduct tokens

Add item to /users/{uid}/inventory/{itemId}

Game integration:

Use equipped skins/effects in games