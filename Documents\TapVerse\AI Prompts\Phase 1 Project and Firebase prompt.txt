“Create a new Flutter project called tapverse. Set up Firebase and Riverpod, and create shared services for auth, firestore, tokens, sounds, vibration, and game navigation.”

Phase 1: Project Initialization & Core Architecture
Objectives:

Set up project, dependencies, and Firebase

Implement global services and navigation

Tasks:

Create Flutter project:

Project Name: TapVerse

Structure: lib/features/games/..., lib/core/...

Add dependencies:

firebase_core, cloud_firestore, firebase_auth

flutter_riverpod, flutter_animate, lottie, vibration, just_audio

Setup Firebase:

Link Firebase project

Add A: google-services.json "C:\Users\<USER>\Downloads\google-services.json" / B: GoogleService-Info.plist "C:\Users\<USER>\Downloads\GoogleService-Info.plist"

A:
To make the google-services.json config values accessible to Firebase SDKs, you need the Google services Gradle plugin.


Kotlin DSL (build.gradle.kts)

Groovy (build.gradle)
Add the plugin as a dependency to your project-level build.gradle.kts file:

Root-level (project-level) Gradle file (<project>/build.gradle.kts):
plugins {
  // ...

  // Add the dependency for the Google services Gradle plugin
  id("com.google.gms.google-services") version "4.4.3" apply false

}
Then, in your module (app-level) build.gradle.kts file, add both the google-services plugin and any Firebase SDKs that you want to use in your app:

Module (app-level) Gradle file (<project>/<app-module>/build.gradle.kts):
plugins {
  id("com.android.application")

  // Add the Google services Gradle plugin
  id("com.google.gms.google-services")

  ...
}

dependencies {
  // Import the Firebase BoM
  implementation(platform("com.google.firebase:firebase-bom:34.0.0"))


  // TODO: Add the dependencies for Firebase products you want to use
  // When using the BoM, don't specify versions in Firebase dependencies
  implementation("com.google.firebase:firebase-analytics")


  // Add the dependencies for any other desired Firebase products
  // https://firebase.google.com/docs/android/setup#available-libraries
}
By using the Firebase Android BoM, your app will always use compatible Firebase library versions. Learn more
After adding the plugin and the desired SDKs, sync your Android project with Gradle files.

B:
Use Swift Package Manager to install and manage Firebase dependencies.

In Xcode, with your app project open, navigate to File > Add Packages
When prompted, enter the Firebase iOS SDK repository URL:
https://github.com/firebase/firebase-ios-sdk
Select the SDK version that you want to use.
We recommend using the default (latest) SDK version, but you can use an older version, if needed.

Choose the Firebase libraries that you want to use.
Make sure to add FirebaseAnalytics. For Analytics without IDFA collection capability, add FirebaseAnalyticsWithoutAdId instead.

After you click Finish, Xcode will automatically begin resolving and downloading your dependencies in the background.


import SwiftUI
import FirebaseCore


class AppDelegate: NSObject, UIApplicationDelegate {
  func application(_ application: UIApplication,
                   didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
    FirebaseApp.configure()

    return true
  }
}

@main
struct YourApp: App {
  // register app delegate for Firebase setup
  @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate


  var body: some Scene {
    WindowGroup {
      NavigationView {
        ContentView()
      }
    }
  }
}

Enable: Firestore, Auth, Analytics

Create Firebase Firestore structure:

Task: Create the Firestore database structure, sample data, and security rules for a Flutter mini-game app called "TapVerse". It includes multiple arcade games, a token reward system, user profiles, and an optional shop.

Set up the following:

---

1. Firestore Collections:

- `/users/{uid}`  
  Fields:
    - displayName (string)
    - tokens (int, default: 0)
    - highScores (map<string, int>)
    - createdAt (server timestamp)

- `/leaderboards/{game_id}/scores/{user_id}`  
  Each `game_id` matches a game slug like `basketball_flick`, `pong`, `snake`.
  Fields:
    - uid (string)
    - displayName (string)
    - score (int)
    - timestamp (server timestamp)

- `/store/items/{item_id}`  
  Fields:
    - name (string)
    - type (string) → e.g. "skin", "effect"
    - price (int)
    - assetPath (string)

- `/users/{uid}/inventory/{item_id}`  
  Fields:
    - ownedAt (server timestamp)

---

2. Firestore Security Rules:

Apply rules that:

- Allow a user to read/write their own user document
- Allow writing scores only to the authenticated user’s own score document
- Allow public read access to leaderboards and store
- Allow authenticated users to read/write their own inventory

Security Rules:
rules_version = '2';
service cloud.firestore {
match /databases/{database}/documents {

lua
match /users/{userId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}

match /leaderboards/{game}/scores/{userId} {
  allow read: if true;
  allow write: if request.auth != null && request.auth.uid == userId;
}

match /store/{document=**} {
  allow read: if true;
}

match /users/{userId}/inventory/{itemId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}
}
}

yaml
Copy
Edit

---

3. Firebase Initialization Data:

Add sample entries:
- A sample user with displayName = "TestUser", 0 tokens.
- A sample leaderboard score for game `basketball_flick`.
- A sample store item with name = "Gold Ball", price = 250.

---

Goal: Ensure TapVerse has the foundational Firestore collections and security rules to support token eco

Collections: users, leaderboards, store, inventory

Apply security rules

Create global services:

AuthService, FirestoreService, TokenService

Global GameOverDialog, sound manager, vibration service

Setup routing and navigation:

Main screen: game grid with icons and scores

Leaderboard & token count on app bar