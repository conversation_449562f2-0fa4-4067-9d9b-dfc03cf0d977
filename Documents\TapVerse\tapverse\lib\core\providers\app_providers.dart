import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';
import '../services/token_service.dart';
import '../services/sound_service.dart';
import '../services/vibration_service.dart';
import '../services/game_navigation_service.dart';
import '../../features/games/screens/home_screen.dart';
import '../../features/auth/screens/login_screen.dart';

// Core Services
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final firestoreServiceProvider = Provider<FirestoreService>((ref) {
  return FirestoreService();
});

final tokenServiceProvider = Provider<TokenService>((ref) {
  final firestore = ref.watch(firestoreServiceProvider);
  final auth = ref.watch(authServiceProvider);
  return TokenService(firestore, auth);
});

final soundServiceProvider = Provider<SoundService>((ref) {
  return SoundService();
});

final vibrationServiceProvider = Provider<VibrationService>((ref) {
  return VibrationService();
});

final gameNavigationServiceProvider = Provider<GameNavigationService>((ref) {
  return GameNavigationService();
});

// Router Configuration
final routerProvider = Provider<GoRouter>((ref) {
  final auth = ref.watch(authServiceProvider);
  
  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final isLoggedIn = auth.currentUser != null;
      final isLoggingIn = state.matchedLocation == '/login';
      
      if (!isLoggedIn && !isLoggingIn) {
        return '/login';
      }
      
      if (isLoggedIn && isLoggingIn) {
        return '/';
      }
      
      return null;
    },
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
    ],
  );
});

// Auth State Provider
final authStateProvider = StreamProvider((ref) {
  final auth = ref.watch(authServiceProvider);
  return auth.authStateChanges;
});

// User Token Provider
final userTokensProvider = StreamProvider<int>((ref) {
  final tokenService = ref.watch(tokenServiceProvider);
  final auth = ref.watch(authServiceProvider);
  
  final user = auth.currentUser;
  if (user == null) {
    return Stream.value(0);
  }
  
  return tokenService.getUserTokensStream(user.uid);
});
