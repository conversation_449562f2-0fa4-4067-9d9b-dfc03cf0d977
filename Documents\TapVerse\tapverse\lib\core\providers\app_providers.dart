import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';
import '../services/token_service.dart';
import '../services/store_service.dart';
import '../services/equipped_items_service.dart';
import '../services/sound_service.dart';
import '../services/vibration_service.dart';
import '../services/animation_service.dart';
import '../services/daily_reward_service.dart';
import '../services/game_streak_service.dart';
import '../services/analytics_service.dart';
import '../services/account_upgrade_service.dart';
import '../services/game_navigation_service.dart';
import '../services/settings_service.dart';
import '../services/user_stats_service.dart';
import '../services/achievement_service.dart';
import '../services/casino_service.dart';
import '../services/pinned_games_service.dart';
import '../services/user_switching_service.dart';
import '../models/achievement.dart';
import '../models/store_item.dart';
import '../models/user_model.dart';
import '../models/inventory_item.dart';
import '../../features/games/screens/home_screen.dart';
import '../../features/games/screens/game_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/splash_screen.dart';
import '../../features/store/store_screen.dart';
import '../../features/store/screens/inventory_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/settings_screen.dart';
import '../../features/leaderboard/screens/leaderboard_screen.dart';
import '../../features/leaderboard/screens/game_leaderboard_screen.dart';
import '../../features/casino/screens/casino_screen.dart';
import '../../features/casino/screens/spin_wheel_screen.dart';
import '../../features/casino/screens/high_low_screen.dart';
import '../../features/casino/screens/lucky_darts_screen.dart';
import '../../features/casino/screens/roll_risk_screen.dart';
import '../../features/casino/screens/mystery_multiplier_screen.dart';

// Core Services
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final firestoreServiceProvider = Provider<FirestoreService>((ref) {
  return FirestoreService();
});

final tokenServiceProvider = Provider<TokenService>((ref) {
  final firestore = ref.watch(firestoreServiceProvider);
  final auth = ref.watch(authServiceProvider);
  return TokenService(firestore, auth);
});

final soundServiceProvider = Provider<SoundService>((ref) {
  return SoundService();
});

final vibrationServiceProvider = Provider<VibrationService>((ref) {
  return VibrationService();
});

final animationServiceProvider = Provider<AnimationService>((ref) {
  return AnimationService();
});

final dailyRewardServiceProvider = Provider<DailyRewardService>((ref) {
  return DailyRewardService(
    authService: ref.watch(authServiceProvider),
    tokenService: ref.watch(tokenServiceProvider),
  );
});

final gameStreakServiceProvider = Provider<GameStreakService>((ref) {
  return GameStreakService(
    authService: ref.watch(authServiceProvider),
    tokenService: ref.watch(tokenServiceProvider),
  );
});

final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

final accountUpgradeServiceProvider = Provider<AccountUpgradeService>((ref) {
  return AccountUpgradeService(
    authService: ref.watch(authServiceProvider),
    firestoreService: ref.watch(firestoreServiceProvider),
  );
});

final gameNavigationServiceProvider = Provider<GameNavigationService>((ref) {
  return GameNavigationService();
});

final storeServiceProvider = Provider<StoreService>((ref) {
  final firestore = ref.watch(firestoreServiceProvider);
  final auth = ref.watch(authServiceProvider);
  final tokenService = ref.watch(tokenServiceProvider);
  return StoreService(firestore, auth, tokenService);
});

final equippedItemsServiceProvider = Provider<EquippedItemsService>((ref) {
  final firestore = ref.watch(firestoreServiceProvider);
  final auth = ref.watch(authServiceProvider);
  return EquippedItemsService(firestore, auth);
});

final settingsServiceProvider = Provider<SettingsService>((ref) {
  return SettingsService();
});

final userStatsServiceProvider = Provider<UserStatsService>((ref) {
  return UserStatsService();
});

final achievementServiceProvider = Provider<AchievementService>((ref) {
  final tokenService = ref.watch(tokenServiceProvider);
  final authService = ref.watch(authServiceProvider);
  return AchievementService(tokenService, authService);
});

final casinoServiceProvider = Provider<CasinoService>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final authService = ref.watch(authServiceProvider);
  return CasinoService(firestoreService, authService);
});

final pinnedGamesServiceProvider = Provider<PinnedGamesService>((ref) {
  return PinnedGamesService();
});

final userSwitchingServiceProvider = Provider<UserSwitchingService>((ref) {
  final authService = ref.watch(authServiceProvider);
  return UserSwitchingService(authService);
});

// Achievement progress provider
final userAchievementProgressProvider = StreamProvider<Map<String, AchievementProgress>>((ref) {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) {
      if (user == null) {
        return Stream.value(<String, AchievementProgress>{});
      }

      final achievementService = ref.watch(achievementServiceProvider);
      return achievementService.getUserAchievementProgressStream(user.uid);
    },
    loading: () => Stream.value(<String, AchievementProgress>{}),
    error: (_, __) => Stream.value(<String, AchievementProgress>{}),
  );
});

// Router Configuration
final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);

  return GoRouter(
    initialLocation: '/splash',
    refreshListenable: _AuthStateNotifier(ref),
    redirect: (context, state) {
      return authState.when(
        data: (user) {
          final isLoggedIn = user != null;
          final currentPath = state.matchedLocation;

          // Allow splash screen to handle initial authentication
          if (currentPath == '/splash') {
            return null;
          }

          // If not logged in and trying to access protected routes, redirect to splash
          if (!isLoggedIn && currentPath != '/login') {
            return '/splash';
          }

          // If logged in and trying to access login, redirect to home
          if (isLoggedIn && currentPath == '/login') {
            return '/';
          }

          return null;
        },
        loading: () => null, // Stay on current route while loading
        error: (_, __) => '/splash', // Redirect to splash on error
      );
    },
    routes: [
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/game/:gameId',
        builder: (context, state) {
          final gameId = state.pathParameters['gameId']!;
          return GameScreen(gameId: gameId);
        },
      ),
      GoRoute(
        path: '/store',
        builder: (context, state) => const StoreScreen(),
      ),
      GoRoute(
        path: '/inventory',
        builder: (context, state) => const InventoryScreen(),
      ),
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: '/leaderboard',
        builder: (context, state) => const LeaderboardScreen(),
      ),
      GoRoute(
        path: '/leaderboard/:gameId',
        builder: (context, state) {
          final gameId = state.pathParameters['gameId']!;
          return GameLeaderboardScreen(gameId: gameId);
        },
      ),
      GoRoute(
        path: '/casino',
        builder: (context, state) => const CasinoScreen(),
      ),
      GoRoute(
        path: '/casino/spin_wheel',
        builder: (context, state) => const SpinWheelScreen(),
      ),
      GoRoute(
        path: '/casino/high_or_low',
        builder: (context, state) => const HighLowScreen(),
      ),
      GoRoute(
        path: '/casino/lucky_darts',
        builder: (context, state) => const LuckyDartsScreen(),
      ),
      GoRoute(
        path: '/casino/roll_n_risk',
        builder: (context, state) => const RollRiskScreen(),
      ),
      GoRoute(
        path: '/casino/mystery_multiplier',
        builder: (context, state) => const MysteryMultiplierScreen(),
      ),
    ],
  );
});

// Helper class to notify router of auth state changes
class _AuthStateNotifier extends ChangeNotifier {
  final Ref _ref;

  _AuthStateNotifier(this._ref) {
    // Listen to auth state changes and notify router
    _ref.listen(authStateProvider, (previous, next) {
      notifyListeners();
    });
  }
}

// Auth State Provider
final authStateProvider = StreamProvider((ref) {
  final auth = ref.watch(authServiceProvider);
  return auth.authStateChanges;
});

// User Token Provider
final userTokensProvider = StreamProvider<int>((ref) {
  final tokenService = ref.watch(tokenServiceProvider);
  final auth = ref.watch(authServiceProvider);

  final user = auth.currentUser;
  if (user == null) {
    return Stream.value(0);
  }

  return tokenService.getUserTokensStream(user.uid);
});

// Store Providers
final storeItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getStoreItems();
});

final userInventoryProvider = StreamProvider<List<String>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getUserInventory();
});

final featuredItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getFeaturedItems();
});

// Store items by type providers
final skinItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getStoreItemsByType(StoreItemType.skin);
});

final effectItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getStoreItemsByType(StoreItemType.effect);
});

final themeItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getStoreItemsByType(StoreItemType.theme);
});

final powerupItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getStoreItemsByType(StoreItemType.powerup);
});

final soundItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getStoreItemsByType(StoreItemType.sound);
});

// New comprehensive store providers
final cosmeticItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getCosmeticItems();
});

final gameUnlockItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getGameUnlockItems();
});

final flairItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getFlairItems();
});

final mysteryDrawItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getMysteryDrawItems();
});

final tokenBoostItemsProvider = StreamProvider<List<StoreItem>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getTokenBoostItems();
});

// Active customizations providers
final activeSkinsProvider = StreamProvider<Map<String, String>>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getActiveSkins();
});

final activeFlairProvider = StreamProvider<String?>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getActiveFlair();
});

final activeBoostProvider = StreamProvider<Map<String, dynamic>?>((ref) {
  final storeService = ref.watch(storeServiceProvider);
  return storeService.getActiveBoost();
});

// Equipped Items Providers
final equippedItemsProvider = FutureProvider<Map<StoreItemType, String?>>((ref) {
  final equippedItemsService = ref.watch(equippedItemsServiceProvider);
  return equippedItemsService.getAllEquippedItems();
});

final equippedSkinProvider = FutureProvider<String?>((ref) {
  final equippedItemsService = ref.watch(equippedItemsServiceProvider);
  return equippedItemsService.getEquippedItem(StoreItemType.skin);
});

final equippedEffectProvider = FutureProvider<String?>((ref) {
  final equippedItemsService = ref.watch(equippedItemsServiceProvider);
  return equippedItemsService.getEquippedItem(StoreItemType.effect);
});

final equippedThemeProvider = FutureProvider<String?>((ref) {
  final equippedItemsService = ref.watch(equippedItemsServiceProvider);
  return equippedItemsService.getEquippedItem(StoreItemType.theme);
});

final equippedSoundProvider = FutureProvider<String?>((ref) {
  final equippedItemsService = ref.watch(equippedItemsServiceProvider);
  return equippedItemsService.getEquippedItem(StoreItemType.sound);
});

// User Data Providers
final userDataProvider = StreamProvider<UserModel?>((ref) {
  final userStatsService = ref.watch(userStatsServiceProvider);
  final auth = ref.watch(authServiceProvider);

  final user = auth.currentUser;
  if (user == null) {
    return Stream.value(null);
  }

  return userStatsService.getUserDataStream(user.uid);
});

final userInventoryItemsProvider = StreamProvider<List<InventoryItem>>((ref) {
  final userStatsService = ref.watch(userStatsServiceProvider);
  final auth = ref.watch(authServiceProvider);

  final user = auth.currentUser;
  if (user == null) {
    return Stream.value([]);
  }

  return userStatsService.getUserInventoryStream(user.uid);
});

final userStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) {
  final userStatsService = ref.watch(userStatsServiceProvider);
  final auth = ref.watch(authServiceProvider);

  final user = auth.currentUser;
  if (user == null) {
    return Future.value({});
  }

  return userStatsService.getUserStatistics(user.uid);
});

final userAchievementsProvider = FutureProvider<Map<String, bool>>((ref) {
  final userStatsService = ref.watch(userStatsServiceProvider);
  final auth = ref.watch(authServiceProvider);

  final user = auth.currentUser;
  if (user == null) {
    return Future.value({});
  }

  return userStatsService.getUserAchievements(user.uid);
});
