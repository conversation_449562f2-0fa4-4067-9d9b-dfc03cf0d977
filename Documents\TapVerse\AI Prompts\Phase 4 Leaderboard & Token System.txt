Phase 4: Leaderboard & Token System

“Create a LeaderboardWidget that displays top 10 users for a given game ID from Firestore /leaderboards/{game}/scores. Show displayName and score.”

Objectives:

Real-time leaderboard & token rewards

Global rewards tracking

Tasks:

Create global token service:

Update /users/{uid}/tokens

Grant tokens = score * multiplier

Global leaderboard screen:

Show top 10 for each game

Path: /leaderboards/{game}/scores

Per-game leaderboard popup