“Create a BaseGameWidget in Flutter with score tracking, game loop, pause button, game over dialog, and token reward integration. Use Riverpod.”

Phase 2: Game Engine & UI Foundation
Objectives:

Build shared components used across all games

Tasks:

Create shared game engine base:

BaseGameWidget with lifecycle handling

Game UI layout with score, pause, game over overlay

GameOverDialog component:

Shows score, high score, tokens earned

Buttons: Replay, Exit, Leaderboard

Shared utils:

Scoring system

Vibration & sound utils

Firestore score submission logic

Token reward updater