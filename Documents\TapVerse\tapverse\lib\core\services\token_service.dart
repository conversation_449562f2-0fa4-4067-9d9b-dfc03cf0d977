import 'firestore_service.dart';
import 'auth_service.dart';

class TokenService {
  final FirestoreService _firestore;
  final AuthService _auth;

  TokenService(this._firestore, this._auth);

  // Get user tokens
  Future<int> getUserTokens(String uid) async {
    try {
      final user = await _firestore.getUser(uid);
      return user?.tokens ?? 0;
    } catch (e) {
      print('Error getting user tokens: $e');
      return 0;
    }
  }

  // Get user tokens stream
  Stream<int> getUserTokensStream(String uid) {
    return _firestore.getUserStream(uid).map((user) => user?.tokens ?? 0);
  }

  // Award tokens
  Future<void> awardTokens(String uid, int amount, {String? reason}) async {
    try {
      final currentTokens = await getUserTokens(uid);
      final newTokens = currentTokens + amount;
      await _firestore.updateUserTokens(uid, newTokens);
      
      print('Awarded $amount tokens to user $uid. Reason: ${reason ?? "Game completion"}');
    } catch (e) {
      print('Error awarding tokens: $e');
    }
  }

  // Spend tokens
  Future<bool> spendTokens(String uid, int amount, {String? reason}) async {
    try {
      final currentTokens = await getUserTokens(uid);
      
      if (currentTokens < amount) {
        print('Insufficient tokens. Required: $amount, Available: $currentTokens');
        return false;
      }
      
      final newTokens = currentTokens - amount;
      await _firestore.updateUserTokens(uid, newTokens);
      
      print('Spent $amount tokens for user $uid. Reason: ${reason ?? "Purchase"}');
      return true;
    } catch (e) {
      print('Error spending tokens: $e');
      return false;
    }
  }

  // Calculate tokens based on score
  int calculateTokenReward(int score, String gameType) {
    // Improved base token calculation - more generous rewards
    int baseTokens = (score / 25).floor(); // Changed from 100 to 25 for 4x more base tokens

    // Enhanced game-specific multipliers
    double multiplier = switch (gameType) {
      'basketball_flick' => 2.5,      // Increased from 1.0
      'pong' => 3.0,                  // Increased from 1.2
      'snake' => 3.5,                 // Increased from 1.5
      'tetris' => 4.5,                // Increased from 2.0
      'flappy_bird' => 3.2,           // Increased from 1.3
      'space_invaders' => 4.0,        // Increased from 1.8
      'pac_man' => 4.8,               // Increased from 2.2
      'frogger' => 3.6,               // Increased from 1.6
      'breakout' => 3.8,              // Increased from 1.4
      'asteroids' => 4.2,             // Increased from 1.7
      'catch_game' => 2.7,            // New game
      'darts_flick' => 3.3,           // New game
      'dropper' => 2.9,               // New game
      'memory_match' => 3.1,          // New game
      'number_merge' => 3.6,          // New game
      'minesweeper' => 4.0,           // New game
      'sky_hop' => 2.8,               // New game
      'nano_golf' => 3.4,             // New game
      'bullseye' => 3.7,              // New game
      _ => 2.5,                       // Increased default from 1.0
    };

    int finalTokens = (baseTokens * multiplier).round();

    // Improved minimum reward - more generous
    return finalTokens < 5 ? 5 : finalTokens;
  }

  // Add tokens to current user (convenience method)
  Future<void> addTokens(int amount, String reason) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      await awardTokens(user.uid, amount, reason: reason);
    } catch (e) {
      print('Error adding tokens: $e');
    }
  }

  // Award tokens for game completion
  Future<void> awardGameTokens(String gameId, int score) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final tokenReward = calculateTokenReward(score, gameId);
      await awardTokens(user.uid, tokenReward, reason: 'Completed $gameId with score $score');
    } catch (e) {
      print('Error awarding game tokens: $e');
    }
  }

  // Award daily bonus tokens
  Future<void> awardDailyBonus(String uid) async {
    try {
      const dailyBonusAmount = 50;
      await awardTokens(uid, dailyBonusAmount, reason: 'Daily bonus');
    } catch (e) {
      print('Error awarding daily bonus: $e');
    }
  }

  // Award achievement tokens
  Future<void> awardAchievementTokens(String uid, String achievementId, int amount) async {
    try {
      await awardTokens(uid, amount, reason: 'Achievement: $achievementId');
    } catch (e) {
      print('Error awarding achievement tokens: $e');
    }
  }

  // Check if user can afford item
  Future<bool> canAfford(String uid, int cost) async {
    try {
      final currentTokens = await getUserTokens(uid);
      return currentTokens >= cost;
    } catch (e) {
      print('Error checking if user can afford item: $e');
      return false;
    }
  }

  // Get token transaction history (if needed for future implementation)
  Future<List<Map<String, dynamic>>> getTokenHistory(String uid) async {
    // This would require a separate collection to track token transactions
    // For now, return empty list
    return [];
  }

  // Validate token transaction
  bool _validateTokenAmount(int amount) {
    return amount > 0 && amount <= 10000; // Reasonable limits
  }

  // Award tokens with validation
  Future<void> awardTokensSafe(String uid, int amount, {String? reason}) async {
    if (!_validateTokenAmount(amount)) {
      print('Invalid token amount: $amount');
      return;
    }
    
    await awardTokens(uid, amount, reason: reason);
  }

  // Spend tokens with validation
  Future<bool> spendTokensSafe(String uid, int amount, {String? reason}) async {
    if (!_validateTokenAmount(amount)) {
      print('Invalid token amount: $amount');
      return false;
    }
    
    return await spendTokens(uid, amount, reason: reason);
  }
}
